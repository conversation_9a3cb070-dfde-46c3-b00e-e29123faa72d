#!/usr/bin/env python3
"""
测试使用LAQ结果训练的LAPA模型
基于LAPA官方推理接口
"""

import os
import sys
import json
import argparse
from pathlib import Path

# 添加LAPA路径
sys.path.append('/home/<USER>/johnny_ws/lapa_ws/LAPA')

try:
    import jax
    import jax.numpy as jnp
    from latent_pretraining.delta_llama import VideoLLaMAConfig, FlaxDeltaLaMAForCausalLMModule
    from latent_pretraining.data import DeltaVisionTextProcessor
    from tux import load_pickle
except ImportError as e:
    print(f"导入LAPA模块失败: {e}")
    print("请确保LAPA路径正确设置，并安装了JAX")
    sys.exit(1)

def load_test_data(test_file: str, max_samples: int = None) -> list:
    """加载测试数据"""
    test_data = []
    with open(test_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                test_data.append(json.loads(line.strip()))
                if max_samples and len(test_data) >= max_samples:
                    break
    return test_data

def create_simple_test_sample():
    """创建简单的测试样本"""
    return {
        'instruction': '<s> You are a helpful assistant. USER: Predict the action between these two frames ASSISTANT:',
        'vision': [],  # 空的vision tokens
        'delta': ['3', '2', '6', '5'],  # 示例delta tokens
        'fields': '[instruction],[vision],delta'
    }

def test_data_processing():
    """测试数据处理流程"""
    print("=== 测试数据处理 ===")
    
    # 创建处理器配置
    processor_config = DeltaVisionTextProcessor.get_default_config({
        'fields_from_example': 'fields',
        'n_tokens_per_delta': 4,
        'max_n_frames': 1,
        'img_aug': False
    })
    
    # 创建虚拟tokenizer（用于测试）
    class MockTokenizer:
        def __init__(self):
            self.bos_token_id = 1
            self.eos_token_id = 2
            
        def encode(self, text):
            # 简单的mock编码
            return [1] + list(range(3, 3 + len(text.split())))
    
    tokenizer = MockTokenizer()
    processor = DeltaVisionTextProcessor(processor_config, tokenizer)
    
    # 测试样本
    test_sample = create_simple_test_sample()
    print(f"输入样本: {test_sample}")
    
    try:
        # 处理数据
        result = processor(test_sample)
        print(f"处理结果长度: {len(result)}")
        print(f"Token buffer长度: {len(result[0])}")
        print(f"Loss mask长度: {len(result[1])}")
        print(f"Vision mask长度: {len(result[2])}")
        print(f"Delta mask长度: {len(result[3])}")
        print("数据处理测试通过 ✓")
        return True
    except Exception as e:
        print(f"数据处理测试失败: {e}")
        return False

def test_model_loading(model_path: str):
    """测试模型加载"""
    print(f"\n=== 测试模型加载 ===")
    print(f"模型路径: {model_path}")
    
    if not os.path.exists(model_path):
        print(f"模型路径不存在: {model_path}")
        return False
    
    try:
        # 加载模型配置
        config = VideoLLaMAConfig.load_config('7b')
        config.update(dict(
            delta_vocab_size=8,
            max_sequence_length=384
        ))
        
        print(f"模型配置: delta_vocab_size={config.delta_vocab_size}")
        print("模型配置加载成功 ✓")
        return True
        
    except Exception as e:
        print(f"模型加载测试失败: {e}")
        return False

def analyze_training_data(data_file: str):
    """分析训练数据"""
    print(f"\n=== 分析训练数据 ===")
    
    if not os.path.exists(data_file):
        print(f"数据文件不存在: {data_file}")
        return
    
    data = load_test_data(data_file, max_samples=1000)
    print(f"加载数据样本: {len(data)} 条")
    
    # 分析delta tokens
    all_deltas = []
    delta_lengths = []
    
    for item in data:
        if 'delta' in item:
            deltas = item['delta']
            all_deltas.extend(deltas)
            delta_lengths.append(len(deltas))
    
    if all_deltas:
        unique_tokens = set(all_deltas)
        print(f"唯一delta tokens: {len(unique_tokens)}")
        print(f"Token范围: {min(map(int, unique_tokens))} - {max(map(int, unique_tokens))}")
        print(f"平均序列长度: {sum(delta_lengths) / len(delta_lengths):.2f}")
        print(f"序列长度范围: {min(delta_lengths)} - {max(delta_lengths)}")
        
        # 显示样本
        print(f"\n样本数据:")
        sample = data[0]
        print(f"  指令: {sample.get('instruction', '')[:100]}...")
        print(f"  Delta tokens: {sample.get('delta', [])}")
        print(f"  Fields: {sample.get('fields', '')}")

def main():
    parser = argparse.ArgumentParser(description='测试LAPA-LAQ模型')
    parser.add_argument('--model_path', help='训练好的模型路径')
    parser.add_argument('--data_file', help='训练数据文件路径')
    parser.add_argument('--test_data_processing', action='store_true', help='测试数据处理')
    parser.add_argument('--test_model_loading', action='store_true', help='测试模型加载')
    parser.add_argument('--analyze_data', action='store_true', help='分析训练数据')
    
    args = parser.parse_args()
    
    print("=== LAPA-LAQ模型测试工具 ===")
    
    success_count = 0
    total_tests = 0
    
    # 测试数据处理
    if args.test_data_processing:
        total_tests += 1
        if test_data_processing():
            success_count += 1
    
    # 测试模型加载
    if args.test_model_loading and args.model_path:
        total_tests += 1
        if test_model_loading(args.model_path):
            success_count += 1
    
    # 分析数据
    if args.analyze_data and args.data_file:
        analyze_training_data(args.data_file)
    
    # 总结
    if total_tests > 0:
        print(f"\n=== 测试总结 ===")
        print(f"通过测试: {success_count}/{total_tests}")
        if success_count == total_tests:
            print("所有测试通过 ✓")
        else:
            print("部分测试失败 ✗")
    
    # 如果没有指定具体测试，运行基本测试
    if not any([args.test_data_processing, args.test_model_loading, args.analyze_data]):
        print("\n运行基本测试...")
        test_data_processing()
        
        if args.data_file:
            analyze_training_data(args.data_file)

if __name__ == '__main__':
    main()
