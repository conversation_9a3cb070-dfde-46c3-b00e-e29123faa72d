#!/usr/bin/env python3
"""
测试使用LAQ结果训练的LAPA模型
"""

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Any, Tuple
import torch
import numpy as np
from PIL import Image
from transformers import AutoTokenizer, AutoProcessor
import sys

# 添加LAPA路径
sys.path.append('/home/<USER>/johnny_ws/lapa_ws/LAPA')

try:
    from latent_pretraining.delta_llama_action import DeltaLlamaForCausalLM
    from latent_pretraining.processors import DeltaVisionProcessor
except ImportError as e:
    print(f"导入LAPA模块失败: {e}")
    print("请确保LAPA路径正确设置")
    sys.exit(1)

class LAPALAQModel:
    """使用LAQ结果训练的LAPA模型"""
    
    def __init__(self, model_path: str, device: str = 'cuda'):
        self.device = device
        self.model_path = model_path
        
        # 加载模型和处理器
        self.model = self._load_model()
        self.processor = self._load_processor()
        
    def _load_model(self):
        """加载训练好的模型"""
        print(f"加载模型: {self.model_path}")
        
        # 加载模型配置
        config_path = os.path.join(self.model_path, 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config = json.load(f)
        else:
            # 使用默认配置
            config = {
                'delta_vocab_size': 8,
                'modality': 'vision,delta'
            }
        
        # 加载模型
        model = DeltaLlamaForCausalLM.from_pretrained(
            self.model_path,
            torch_dtype=torch.float16,
            device_map=self.device
        )
        model.eval()
        
        return model
    
    def _load_processor(self):
        """加载数据处理器"""
        processor = DeltaVisionProcessor()
        return processor
    
    def predict_delta(self, image_path: str, next_image_path: str, instruction: str) -> List[str]:
        """预测两帧之间的delta tokens"""
        
        # 加载图像
        try:
            image = Image.open(image_path).convert('RGB')
            next_image = Image.open(next_image_path).convert('RGB')
        except Exception as e:
            print(f"图像加载失败: {e}")
            return []
        
        # 准备输入数据
        input_data = {
            'image': image,
            'next_image': next_image,
            'instruction': instruction,
            'vision': '',
            'fields': '[instruction],[vision],delta'
        }
        
        # 处理输入
        processed_input = self.processor(input_data)
        
        # 转换为tensor并移动到设备
        input_ids = torch.tensor(processed_input['input_ids']).unsqueeze(0).to(self.device)
        attention_mask = torch.tensor(processed_input['attention_mask']).unsqueeze(0).to(self.device)
        pixel_values = torch.tensor(processed_input['pixel_values']).unsqueeze(0).to(self.device)
        
        # 模型推理
        with torch.no_grad():
            outputs = self.model(
                input_ids=input_ids,
                attention_mask=attention_mask,
                pixel_values=pixel_values
            )
            
            # 获取delta预测
            delta_logits = outputs.delta_logits
            delta_predictions = torch.argmax(delta_logits, dim=-1)
            
            # 转换为字符串列表
            delta_tokens = [str(token.item()) for token in delta_predictions[0]]
            
        return delta_tokens
    
    def batch_predict(self, test_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量预测"""
        results = []
        
        for i, item in enumerate(test_data):
            if i % 100 == 0:
                print(f"处理进度: {i}/{len(test_data)}")
            
            try:
                predicted_delta = self.predict_delta(
                    item['image'],
                    item['next_image'], 
                    item['instruction']
                )
                
                result = {
                    'id': item['id'],
                    'image': item['image'],
                    'next_image': item['next_image'],
                    'instruction': item['instruction'],
                    'ground_truth_delta': item['delta'],
                    'predicted_delta': predicted_delta,
                    'match': predicted_delta == item['delta']
                }
                
                results.append(result)
                
            except Exception as e:
                print(f"预测失败 {item['id']}: {e}")
                continue
        
        return results

def calculate_metrics(results: List[Dict[str, Any]]) -> Dict[str, float]:
    """计算评估指标"""
    if not results:
        return {}
    
    # 完全匹配准确率
    exact_matches = sum(1 for r in results if r['match'])
    exact_accuracy = exact_matches / len(results)
    
    # Token级别准确率
    total_tokens = 0
    correct_tokens = 0
    
    for result in results:
        gt_tokens = result['ground_truth_delta']
        pred_tokens = result['predicted_delta']
        
        # 确保长度一致
        min_len = min(len(gt_tokens), len(pred_tokens))
        
        for i in range(min_len):
            total_tokens += 1
            if gt_tokens[i] == pred_tokens[i]:
                correct_tokens += 1
    
    token_accuracy = correct_tokens / total_tokens if total_tokens > 0 else 0
    
    # 序列长度准确率
    length_matches = sum(1 for r in results 
                        if len(r['ground_truth_delta']) == len(r['predicted_delta']))
    length_accuracy = length_matches / len(results)
    
    metrics = {
        'exact_accuracy': exact_accuracy,
        'token_accuracy': token_accuracy,
        'length_accuracy': length_accuracy,
        'total_samples': len(results),
        'exact_matches': exact_matches,
        'correct_tokens': correct_tokens,
        'total_tokens': total_tokens
    }
    
    return metrics

def main():
    parser = argparse.ArgumentParser(description='测试LAQ训练的LAPA模型')
    parser.add_argument('--model_path', required=True, help='训练好的模型路径')
    parser.add_argument('--test_data', required=True, help='测试数据文件')
    parser.add_argument('--output_file', required=True, help='输出结果文件')
    parser.add_argument('--device', default='cuda', help='计算设备')
    parser.add_argument('--max_samples', type=int, default=None, help='最大测试样本数')
    
    args = parser.parse_args()
    
    print("=== LAPA-LAQ模型测试 ===")
    print(f"模型路径: {args.model_path}")
    print(f"测试数据: {args.test_data}")
    print(f"输出文件: {args.output_file}")
    
    # 加载测试数据
    test_data = []
    with open(args.test_data, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                test_data.append(json.loads(line.strip()))
    
    if args.max_samples:
        test_data = test_data[:args.max_samples]
    
    print(f"加载测试数据: {len(test_data)} 条")
    
    # 初始化模型
    model = LAPALAQModel(args.model_path, args.device)
    
    # 批量预测
    print("开始预测...")
    results = model.batch_predict(test_data)
    
    # 计算指标
    metrics = calculate_metrics(results)
    
    print("\n=== 评估结果 ===")
    print(f"完全匹配准确率: {metrics['exact_accuracy']:.4f} ({metrics['exact_matches']}/{metrics['total_samples']})")
    print(f"Token级别准确率: {metrics['token_accuracy']:.4f} ({metrics['correct_tokens']}/{metrics['total_tokens']})")
    print(f"序列长度准确率: {metrics['length_accuracy']:.4f}")
    
    # 保存结果
    output_data = {
        'metrics': metrics,
        'results': results,
        'model_path': args.model_path,
        'test_data_path': args.test_data
    }
    
    with open(args.output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n结果已保存到: {args.output_file}")

if __name__ == '__main__':
    main()
