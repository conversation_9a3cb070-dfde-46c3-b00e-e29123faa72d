#!/usr/bin/env python3
"""
数据预处理脚本：将LAQ推理结果转换为LAPA训练格式
"""

import json
import os
import argparse
from pathlib import Path
from typing import Dict, List, Any
import random
from collections import Counter

def load_laq_results(input_file: str) -> List[Dict[str, Any]]:
    """加载LAQ推理结果"""
    results = []
    with open(input_file, 'r', encoding='utf-8') as f:
        for line in f:
            if line.strip():
                results.append(json.loads(line.strip()))
    return results

def validate_delta_tokens(delta_tokens: List[str], vocab_size: int) -> bool:
    """验证delta tokens是否在有效范围内"""
    try:
        for token in delta_tokens:
            token_id = int(token)
            if token_id < 0 or token_id >= vocab_size:
                return False
        return True
    except ValueError:
        return False

def convert_to_lapa_format(laq_data: List[Dict[str, Any]], vocab_size: int) -> List[Dict[str, Any]]:
    """将LAQ结果转换为LAPA训练格式"""
    lapa_data = []
    invalid_count = 0
    
    for item in laq_data:
        # 验证必要字段
        if not all(key in item for key in ['image', 'next_image', 'delta', 'instruction']):
            invalid_count += 1
            continue
            
        # 验证delta tokens
        if not validate_delta_tokens(item['delta'], vocab_size):
            invalid_count += 1
            continue
            
        # 转换为LAPA格式
        lapa_item = {
            'id': item.get('id', f"laq_{len(lapa_data)}"),
            'image': item['image'],
            'next_image': item['next_image'],
            'instruction': item['instruction'],
            'vision': item.get('vision', ''),  # 通常为空
            'delta': item['delta'],  # LAQ预测的delta tokens
            'fields': '[instruction],[vision],delta'
        }
        
        lapa_data.append(lapa_item)
    
    print(f"转换完成: {len(lapa_data)} 条有效数据, {invalid_count} 条无效数据被跳过")
    return lapa_data

def analyze_delta_distribution(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """分析delta tokens的分布"""
    all_tokens = []
    sequence_lengths = []
    
    for item in data:
        delta_tokens = item['delta']
        all_tokens.extend(delta_tokens)
        sequence_lengths.append(len(delta_tokens))
    
    token_counter = Counter(all_tokens)
    length_counter = Counter(sequence_lengths)
    
    analysis = {
        'total_sequences': len(data),
        'total_tokens': len(all_tokens),
        'unique_tokens': len(token_counter),
        'token_distribution': dict(token_counter.most_common(10)),
        'sequence_length_distribution': dict(length_counter),
        'avg_sequence_length': sum(sequence_lengths) / len(sequence_lengths) if sequence_lengths else 0
    }
    
    return analysis

def split_data(data: List[Dict[str, Any]], train_ratio: float = 0.8, val_ratio: float = 0.1) -> tuple:
    """划分训练、验证和测试集"""
    random.shuffle(data)
    
    total = len(data)
    train_end = int(total * train_ratio)
    val_end = int(total * (train_ratio + val_ratio))
    
    train_data = data[:train_end]
    val_data = data[train_end:val_end]
    test_data = data[val_end:]
    
    return train_data, val_data, test_data

def save_jsonl(data: List[Dict[str, Any]], output_file: str):
    """保存为JSONL格式"""
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')

def main():
    parser = argparse.ArgumentParser(description='将LAQ推理结果转换为LAPA训练格式')
    parser.add_argument('--input_file', required=True, help='LAQ推理结果文件路径')
    parser.add_argument('--output_dir', required=True, help='输出目录')
    parser.add_argument('--delta_vocab_size', type=int, default=8, help='Delta词汇表大小')
    parser.add_argument('--train_ratio', type=float, default=0.8, help='训练集比例')
    parser.add_argument('--val_ratio', type=float, default=0.1, help='验证集比例')
    parser.add_argument('--seed', type=int, default=42, help='随机种子')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"开始处理LAQ结果文件: {args.input_file}")
    
    # 加载LAQ结果
    laq_data = load_laq_results(args.input_file)
    print(f"加载了 {len(laq_data)} 条LAQ结果")
    
    # 转换格式
    lapa_data = convert_to_lapa_format(laq_data, args.delta_vocab_size)
    
    if not lapa_data:
        print("错误: 没有有效的数据可以转换")
        return
    
    # 分析数据分布
    analysis = analyze_delta_distribution(lapa_data)
    print("\n=== 数据分析 ===")
    print(f"总序列数: {analysis['total_sequences']}")
    print(f"总token数: {analysis['total_tokens']}")
    print(f"唯一token数: {analysis['unique_tokens']}")
    print(f"平均序列长度: {analysis['avg_sequence_length']:.2f}")
    print(f"序列长度分布: {analysis['sequence_length_distribution']}")
    print(f"高频token: {analysis['token_distribution']}")
    
    # 保存分析结果
    with open(output_dir / 'data_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(analysis, f, ensure_ascii=False, indent=2)
    
    # 划分数据集
    train_data, val_data, test_data = split_data(lapa_data, args.train_ratio, args.val_ratio)
    
    print(f"\n=== 数据划分 ===")
    print(f"训练集: {len(train_data)} 条")
    print(f"验证集: {len(val_data)} 条")
    print(f"测试集: {len(test_data)} 条")
    
    # 保存数据集
    save_jsonl(train_data, output_dir / 'train.jsonl')
    save_jsonl(val_data, output_dir / 'val.jsonl')
    save_jsonl(test_data, output_dir / 'test.jsonl')
    
    # 保存完整数据集（用于后续分析）
    save_jsonl(lapa_data, output_dir / 'full_dataset.jsonl')
    
    print(f"\n=== 处理完成 ===")
    print(f"数据已保存到: {output_dir}")
    print(f"- train.jsonl: {len(train_data)} 条")
    print(f"- val.jsonl: {len(val_data)} 条") 
    print(f"- test.jsonl: {len(test_data)} 条")
    print(f"- full_dataset.jsonl: {len(lapa_data)} 条")
    print(f"- data_analysis.json: 数据分析报告")

if __name__ == '__main__':
    main()
