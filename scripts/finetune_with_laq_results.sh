#!/bin/bash

# LAPA训练脚本 - 使用LAQ推理结果进行latent action预训练
# 基于LAPA官方实现，使用JAX/Flax框架

# 设置基本路径和环境
export SCRIPT_DIR="$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
export PROJECT_DIR="/home/<USER>/johnny_ws/lapa_ws/LAPA"
cd $PROJECT_DIR
export PYTHONPATH="$PYTHONPATH:$PROJECT_DIR"

# 数据路径配置
export DATA_DIR="/home/<USER>/johnny_ws/lapa_ws"
export LAQ_RESULTS_FILE="${DATA_DIR}/results/laq_fast_inference_results.jsonl"
export PROCESSED_DATA_DIR="${DATA_DIR}/data/laq_processed"
export OUTPUT_DIR="${DATA_DIR}/outputs/lapa_laq_training"

# 创建必要的目录
mkdir -p ${PROCESSED_DATA_DIR}
mkdir -p ${OUTPUT_DIR}

# 模型配置（基于LAPA官方配置）
export MODALITY="vision,text,delta"  # 使用vision+text+delta模态（符合LAPA预训练格式）
export DELTA_VOCAB_SIZE=8  # LAQ使用的delta词汇表大小
export BATCH_SIZE=32
export LEARNING_RATE=2e-5
export TOTAL_STEPS=5000

# 检查LAQ结果文件
if [ ! -f "${LAQ_RESULTS_FILE}" ]; then
    echo "错误: LAQ结果文件不存在: ${LAQ_RESULTS_FILE}"
    exit 1
fi

echo "=== LAPA训练 - 使用LAQ结果 ==="
echo "LAQ结果文件: ${LAQ_RESULTS_FILE}"
echo "处理后数据目录: ${PROCESSED_DATA_DIR}"
echo "输出目录: ${OUTPUT_DIR}"

# 第一步：数据预处理 - 将LAQ结果转换为LAPA训练格式
echo "步骤1: 数据预处理..."
python3 ${DATA_DIR}/scripts/preprocess_laq_for_lapa.py \
    --input_file ${LAQ_RESULTS_FILE} \
    --output_file ${PROCESSED_DATA_DIR}/laq_lapa_training.jsonl \
    --delta_vocab_size ${DELTA_VOCAB_SIZE}

# 检查预处理是否成功
if [ ! -f "${PROCESSED_DATA_DIR}/laq_lapa_training.jsonl" ]; then
    echo "错误: 数据预处理失败"
    exit 1
fi

echo "数据预处理完成"

# 第二步：使用LAPA官方训练脚本进行Latent Pretraining
echo "步骤2: LAPA Latent Pretraining..."
python3 -u -m latent_pretraining.train \
    --modality="${MODALITY}" \
    --mesh_dim='1,-1,1,1' \
    --dtype='bf16' \
    --total_steps=${TOTAL_STEPS} \
    --log_freq=10 \
    --eval_steps=0 \
    --save_model_freq=1000 \
    --eval_log_freq=100 \
    --save_milestone_freq=${TOTAL_STEPS} \
    --load_llama_config='7b' \
    --update_llama_config="dict(delta_vocab_size=${DELTA_VOCAB_SIZE},theta=50000000,max_sequence_length=2048,use_flash_attention=False,scan_attention=False,remat_attention='nothing_saveable',scan_mlp=False,remat_mlp='nothing_saveable',remat_block='nothing_saveable',scan_layers=False)" \
    --optimizer.type='adamw' \
    --optimizer.accumulate_gradient_steps=1 \
    --optimizer.adamw_optimizer.weight_decay=0 \
    --optimizer.adamw_optimizer.lr=${LEARNING_RATE} \
    --optimizer.adamw_optimizer.end_lr=${LEARNING_RATE} \
    --optimizer.adamw_optimizer.lr_warmup_steps=100 \
    --optimizer.adamw_optimizer.lr_decay_steps=100 \
    --use_data_sharded_loader=True \
    --train_dataset.type='json_vision_delta' \
    --train_dataset.delta_vision_text_processor.fields_from_example='fields' \
    --train_dataset.delta_vision_text_processor.n_tokens_per_delta=4 \
    --train_dataset.delta_vision_text_processor.max_n_frames=1 \
    --train_dataset.delta_vision_text_processor.img_aug=False \
    --train_dataset.json_delta_dataset.mode="pad" \
    --train_dataset.json_delta_dataset.path="${PROCESSED_DATA_DIR}/laq_lapa_training.jsonl" \
    --train_dataset.json_delta_dataset.seq_length=384 \
    --train_dataset.json_delta_dataset.batch_size=${BATCH_SIZE} \
    --train_dataset.json_delta_dataset.tokenizer_processes=1 \
    --train_dataset.json_delta_dataset.tokenizer_parallel_chunk_size=32 \
    --train_dataset.json_delta_dataset.tokenizer_parallel_batch_size=256 \
    --train_dataset.json_delta_dataset.use_data_sharded_loader=True \
    --checkpointer.save_optimizer_state=False \
    --autoresume=False \
    --logger.append_uuid=False \
    --logger.online=False \
    --logger.project_id="lapa_laq" \
    --logger.experiment_id="laq_latent_pretraining" \
    --logger.experiment_note="LAPA trained with LAQ results" \
    --logger.output_dir="${OUTPUT_DIR}"

echo "LAPA Latent Pretraining完成"

# 第三步：验证训练结果
echo "步骤3: 验证训练结果..."
python3 ${DATA_DIR}/scripts/test_lapa_laq_model.py \
    --test_data_processing \
    --analyze_data \
    --data_file "${PROCESSED_DATA_DIR}/laq_lapa_training.jsonl"

# 检查训练输出
if [ -d "${OUTPUT_DIR}" ]; then
    echo "训练输出目录: ${OUTPUT_DIR}"
    echo "检查点文件:"
    find "${OUTPUT_DIR}" -name "*.pkl" -o -name "*.json" | head -5
else
    echo "警告: 训练输出目录不存在"
fi

echo "=== 训练完成 ==="
echo "训练输出保存在: ${OUTPUT_DIR}"
echo "数据分析结果保存在: ${PROCESSED_DATA_DIR}/laq_data_analysis.json"

# 可选：Action Finetuning（如果有真实动作数据）
if [ -f "${DATA_DIR}/data/real_actions.jsonl" ]; then
    echo ""
    echo "=== 可选步骤：Action Finetuning ==="
    echo "发现真实动作数据，可以进行Action Finetuning："
    echo "python3 -u -m latent_pretraining.train \\"
    echo "    --modality='vision,action' \\"
    echo "    --load_checkpoint='trainstate::${OUTPUT_DIR}/streaming_train_state' \\"
    echo "    --train_dataset.type='json_vision_action' \\"
    echo "    --train_dataset.json_vision_action_dataset.path='${DATA_DIR}/data/real_actions.jsonl'"
else
    echo ""
    echo "提示: 如需进行Action Finetuning，请准备真实动作数据文件"
fi
