#!/bin/bash

# LAPA训练脚本 - 使用LAQ推理结果进行latent action预训练
# 基于finetune_simpler.sh修改，适配LAQ delta tokens

# 设置基本路径
export PYTHONPATH="${PYTHONPATH}:/home/<USER>/johnny_ws/lapa_ws/LAPA"
export CUDA_VISIBLE_DEVICES=0

# 数据路径配置
DATA_DIR="/home/<USER>/johnny_ws/lapa_ws"
LAQ_RESULTS_FILE="${DATA_DIR}/results/laq_fast_inference_results.jsonl"
PROCESSED_DATA_DIR="${DATA_DIR}/data/laq_processed"
CHECKPOINT_DIR="${DATA_DIR}/checkpoints"

# 创建必要的目录
mkdir -p ${PROCESSED_DATA_DIR}
mkdir -p ${CHECKPOINT_DIR}

# 模型配置
MODEL_NAME="lapa_laq_pretrained"
MODALITY="vision,delta"  # 只使用vision和delta模态，不使用action
DELTA_VOCAB_SIZE=8  # LAQ使用的delta词汇表大小
BATCH_SIZE=16
LEARNING_RATE=1e-5
NUM_EPOCHS=5

echo "=== LAPA训练 - 使用LAQ结果 ==="
echo "LAQ结果文件: ${LAQ_RESULTS_FILE}"
echo "处理后数据目录: ${PROCESSED_DATA_DIR}"
echo "模型保存目录: ${CHECKPOINT_DIR}"

# 第一步：数据预处理 - 将LAQ结果转换为LAPA训练格式
echo "步骤1: 数据预处理..."
python3 scripts/preprocess_laq_results.py \
    --input_file ${LAQ_RESULTS_FILE} \
    --output_dir ${PROCESSED_DATA_DIR} \
    --delta_vocab_size ${DELTA_VOCAB_SIZE}

# 检查预处理是否成功
if [ ! -f "${PROCESSED_DATA_DIR}/train.jsonl" ]; then
    echo "错误: 数据预处理失败"
    exit 1
fi

echo "数据预处理完成"

# 第二步：Latent Pretraining - 训练LAPA学习预测LAQ的delta tokens
echo "步骤2: Latent Pretraining..."
python3 scripts/train_lapa_with_laq.py \
    --config configs/laq_training_config.yaml

# 检查训练是否成功
if [ ! -d "${CHECKPOINT_DIR}/${MODEL_NAME}" ]; then
    echo "错误: Latent Pretraining失败"
    exit 1
fi

echo "Latent Pretraining完成"

# 第三步：可选的Action Finetuning（如果你有真实动作数据）
# 这一步将latent actions映射到real actions
if [ -f "${DATA_DIR}/data/real_actions.jsonl" ]; then
    echo "步骤3: Action Finetuning..."
    python3 -m latent_pretraining.train \
        --model_name "${MODEL_NAME}_finetuned" \
        --pretrained_model_path "${CHECKPOINT_DIR}/${MODEL_NAME}" \
        --data_path "${DATA_DIR}/data/real_actions.jsonl" \
        --modality "vision,action" \
        --action_vocab_size 245 \
        --batch_size 8 \
        --learning_rate 5e-6 \
        --num_epochs 3 \
        --checkpoint_dir ${CHECKPOINT_DIR} \
        --processor_type "DeltaVisionActionProcessor" \
        --freeze_vision_encoder true \
        --replace_action_head true
    
    echo "Action Finetuning完成"
else
    echo "跳过Action Finetuning（未找到真实动作数据）"
fi

echo "=== 训练完成 ==="
echo "模型保存在: ${CHECKPOINT_DIR}/${MODEL_NAME}"

# 第四步：模型推理测试
echo "步骤4: 模型推理测试..."
python3 scripts/test_laq_trained_model.py \
    --model_path "${CHECKPOINT_DIR}/${MODEL_NAME}" \
    --test_data "${PROCESSED_DATA_DIR}/test.jsonl" \
    --output_file "${DATA_DIR}/results/lapa_laq_inference_results.jsonl"

echo "推理测试完成，结果保存在: ${DATA_DIR}/results/lapa_laq_inference_results.jsonl"
