#!/usr/bin/env python3
"""
使用LAQ结果训练LAPA模型的主训练脚本
"""

import os
import sys
import yaml
import json
import argparse
import logging
from pathlib import Path
from typing import Dict, Any

# 添加LAPA路径
sys.path.append('/home/<USER>/johnny_ws/lapa_ws/LAPA')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from transformers import (
    AutoTokenizer, 
    AutoProcessor,
    get_cosine_schedule_with_warmup,
    get_linear_schedule_with_warmup
)

try:
    from latent_pretraining.delta_llama_action import DeltaLlamaForCausalLM
    from latent_pretraining.processors import DeltaVisionProcessor
    from latent_pretraining.train import setup_logging, save_checkpoint
except ImportError as e:
    print(f"导入LAPA模块失败: {e}")
    print("请确保LAPA路径正确设置")
    sys.exit(1)

class LAQDataset(torch.utils.data.Dataset):
    """LAQ结果数据集"""
    
    def __init__(self, data_file: str, processor: DeltaVisionProcessor):
        self.processor = processor
        self.data = []
        
        # 加载数据
        with open(data_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    self.data.append(json.loads(line.strip()))
        
        print(f"加载数据集: {len(self.data)} 条记录")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 使用processor处理数据
        processed = self.processor(item)
        
        return {
            'input_ids': torch.tensor(processed['input_ids']),
            'attention_mask': torch.tensor(processed['attention_mask']),
            'pixel_values': torch.tensor(processed['pixel_values']),
            'delta_labels': torch.tensor([int(token) for token in item['delta']]),
            'id': item['id']
        }

class LAPALAQTrainer:
    """LAPA-LAQ训练器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device(config['hardware']['device'])
        
        # 设置日志
        self.setup_logging()
        
        # 初始化模型和处理器
        self.model = self.load_model()
        self.processor = DeltaVisionProcessor()
        
        # 初始化数据集
        self.train_dataset = LAQDataset(
            os.path.join(config['data']['processed_data_dir'], 'train.jsonl'),
            self.processor
        )
        self.val_dataset = LAQDataset(
            os.path.join(config['data']['processed_data_dir'], 'val.jsonl'),
            self.processor
        )
        
        # 初始化数据加载器
        self.train_loader = DataLoader(
            self.train_dataset,
            batch_size=config['training']['batch_size'],
            shuffle=True,
            num_workers=config['hardware']['dataloader_num_workers'],
            pin_memory=config['hardware']['pin_memory']
        )
        self.val_loader = DataLoader(
            self.val_dataset,
            batch_size=config['training']['batch_size'],
            shuffle=False,
            num_workers=config['hardware']['dataloader_num_workers'],
            pin_memory=config['hardware']['pin_memory']
        )
        
        # 初始化优化器和调度器
        self.optimizer = self.setup_optimizer()
        self.scheduler = self.setup_scheduler()
        
        # 训练状态
        self.global_step = 0
        self.epoch = 0
        self.best_val_loss = float('inf')
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path(self.config['output']['log_dir'])
        log_dir.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format=self.config['logging']['format'],
            handlers=[
                logging.FileHandler(log_dir / 'training.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def load_model(self):
        """加载模型"""
        self.logger.info("初始化LAPA模型...")
        
        # 这里需要根据实际的LAPA模型初始化方式调整
        # 假设使用预训练的LWM作为backbone
        model = DeltaLlamaForCausalLM.from_pretrained(
            self.config['model']['base_model'],
            torch_dtype=torch.float16 if self.config['hardware']['mixed_precision'] else torch.float32,
            device_map=self.device
        )
        
        # 冻结vision encoder（如果配置要求）
        if self.config['model']['freeze_vision_encoder']:
            for param in model.vision_encoder.parameters():
                param.requires_grad = False
            self.logger.info("Vision encoder已冻结")
        
        return model
    
    def setup_optimizer(self):
        """设置优化器"""
        optimizer_config = self.config['optimizer']
        
        if optimizer_config['type'] == 'AdamW':
            optimizer = torch.optim.AdamW(
                self.model.parameters(),
                lr=self.config['training']['learning_rate'],
                weight_decay=optimizer_config['weight_decay'],
                betas=(optimizer_config['beta1'], optimizer_config['beta2']),
                eps=optimizer_config['eps']
            )
        else:
            raise ValueError(f"不支持的优化器类型: {optimizer_config['type']}")
        
        return optimizer
    
    def setup_scheduler(self):
        """设置学习率调度器"""
        scheduler_config = self.config['scheduler']
        total_steps = len(self.train_loader) * self.config['training']['num_epochs']
        warmup_steps = int(total_steps * scheduler_config['warmup_ratio'])
        
        if scheduler_config['type'] == 'cosine':
            scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps
            )
        elif scheduler_config['type'] == 'linear':
            scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps
            )
        else:
            raise ValueError(f"不支持的调度器类型: {scheduler_config['type']}")
        
        return scheduler
    
    def train_epoch(self):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0
        
        for batch_idx, batch in enumerate(self.train_loader):
            # 移动数据到设备
            batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                    for k, v in batch.items()}
            
            # 前向传播
            outputs = self.model(
                input_ids=batch['input_ids'],
                attention_mask=batch['attention_mask'],
                pixel_values=batch['pixel_values'],
                delta_labels=batch['delta_labels']
            )
            
            loss = outputs.loss
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.config['training']['max_grad_norm']
            )
            
            # 优化器步骤
            self.optimizer.step()
            self.scheduler.step()
            self.optimizer.zero_grad()
            
            total_loss += loss.item()
            self.global_step += 1
            
            # 日志记录
            if self.global_step % self.config['training']['logging_steps'] == 0:
                self.logger.info(
                    f"Epoch {self.epoch}, Step {self.global_step}, "
                    f"Loss: {loss.item():.4f}, LR: {self.scheduler.get_last_lr()[0]:.2e}"
                )
            
            # 验证
            if self.global_step % self.config['training']['eval_steps'] == 0:
                val_loss = self.validate()
                self.logger.info(f"Validation Loss: {val_loss:.4f}")
                
                # 保存最佳模型
                if val_loss < self.best_val_loss:
                    self.best_val_loss = val_loss
                    self.save_checkpoint('best_model')
            
            # 保存检查点
            if self.global_step % self.config['training']['save_steps'] == 0:
                self.save_checkpoint(f'checkpoint_step_{self.global_step}')
        
        return total_loss / len(self.train_loader)
    
    def validate(self):
        """验证"""
        self.model.eval()
        total_loss = 0
        
        with torch.no_grad():
            for batch in self.val_loader:
                batch = {k: v.to(self.device) if isinstance(v, torch.Tensor) else v 
                        for k, v in batch.items()}
                
                outputs = self.model(
                    input_ids=batch['input_ids'],
                    attention_mask=batch['attention_mask'],
                    pixel_values=batch['pixel_values'],
                    delta_labels=batch['delta_labels']
                )
                
                total_loss += outputs.loss.item()
        
        self.model.train()
        return total_loss / len(self.val_loader)
    
    def save_checkpoint(self, name: str):
        """保存检查点"""
        checkpoint_dir = Path(self.config['output']['checkpoint_dir'])
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        checkpoint_path = checkpoint_dir / name
        
        self.model.save_pretrained(checkpoint_path)
        
        # 保存训练状态
        state = {
            'epoch': self.epoch,
            'global_step': self.global_step,
            'best_val_loss': self.best_val_loss,
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'config': self.config
        }
        
        torch.save(state, checkpoint_path / 'training_state.pt')
        self.logger.info(f"检查点已保存: {checkpoint_path}")
    
    def train(self):
        """主训练循环"""
        self.logger.info("开始训练...")
        
        for epoch in range(self.config['training']['num_epochs']):
            self.epoch = epoch
            self.logger.info(f"开始训练 Epoch {epoch + 1}/{self.config['training']['num_epochs']}")
            
            train_loss = self.train_epoch()
            val_loss = self.validate()
            
            self.logger.info(
                f"Epoch {epoch + 1} 完成 - "
                f"Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}"
            )
            
            # 保存epoch检查点
            self.save_checkpoint(f'epoch_{epoch + 1}')
        
        self.logger.info("训练完成!")

def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def main():
    parser = argparse.ArgumentParser(description='使用LAQ结果训练LAPA模型')
    parser.add_argument('--config', required=True, help='配置文件路径')
    parser.add_argument('--resume', help='恢复训练的检查点路径')
    
    args = parser.parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    # 创建训练器
    trainer = LAPALAQTrainer(config)
    
    # 开始训练
    trainer.train()

if __name__ == '__main__':
    main()
