# 使用LAQ结果训练LAPA模型

## 项目概述

本项目实现了一个创新的训练方案：使用LAQ（Latent Action Quantization）模型的推理结果来监督训练LAPA（Latent Action Pretraining for general Action models）模型。这种方法跳过了LAPA原始方法中的VQ-VAE训练阶段，直接使用预训练的LAQ模型提供的latent action tokens进行监督学习。

## 技术方案

### 核心思想

1. **LAQ作为Oracle**: 使用已训练好的LAQ模型对你的场景数据进行推理，生成latent action tokens
2. **跳过VQ-VAE阶段**: 直接使用LAQ的输出作为ground truth latent actions
3. **Latent Pretraining**: 训练LAPA模型学习从视觉观察预测这些latent actions
4. **可选Action Finetuning**: 如果有真实动作数据，可以进一步微调到real actions

### 与原始LAPA的对比

| 阶段 | 原始LAPA | 本方案 |
|------|----------|--------|
| 第一阶段 | 训练VQ-VAE学习latent actions | ✅ 跳过（使用LAQ结果） |
| 第二阶段 | Latent Pretraining | ✅ 使用LAQ tokens监督训练 |
| 第三阶段 | Action Finetuning | ✅ 可选（如有真实动作数据） |

## 文件结构

```
├── scripts/
│   ├── finetune_with_laq_results.sh      # 主训练脚本
│   ├── preprocess_laq_results.py         # 数据预处理
│   ├── train_lapa_with_laq.py            # 训练主程序
│   └── test_laq_trained_model.py         # 模型测试
├── configs/
│   └── laq_training_config.yaml          # 训练配置
├── data/
│   └── laq_processed/                    # 处理后的训练数据
├── checkpoints/                          # 模型检查点
├── results/                              # 结果文件
└── logs/                                 # 训练日志
```

## 使用方法

### 1. 环境准备

```bash
# 设置Python路径
export PYTHONPATH="${PYTHONPATH}:/home/<USER>/johnny_ws/lapa_ws/LAPA"

# 安装依赖
pip install torch transformers pillow pyyaml numpy
```

### 2. 数据准备

确保你的LAQ推理结果文件格式正确：

```json
{
  "id": "sample_001",
  "image": "path/to/current_frame.jpg",
  "next_image": "path/to/next_frame.jpg", 
  "instruction": "Predict the action between these two frames",
  "vision": "",
  "delta": ["7", "2", "6", "5"],
  "fields": "[instruction],[vision],delta"
}
```

### 3. 运行训练

```bash
# 一键运行完整训练流程
bash scripts/finetune_with_laq_results.sh
```

或者分步执行：

```bash
# 步骤1: 数据预处理
python3 scripts/preprocess_laq_results.py \
    --input_file results/laq_fast_inference_results.jsonl \
    --output_dir data/laq_processed \
    --delta_vocab_size 8

# 步骤2: 模型训练
python3 scripts/train_lapa_with_laq.py \
    --config configs/laq_training_config.yaml

# 步骤3: 模型测试
python3 scripts/test_laq_trained_model.py \
    --model_path checkpoints/lapa_laq_pretrained \
    --test_data data/laq_processed/test.jsonl \
    --output_file results/lapa_laq_inference_results.jsonl
```

### 4. 配置调整

编辑 `configs/laq_training_config.yaml` 来调整训练参数：

```yaml
# 关键配置项
model:
  delta_vocab_size: 8        # 匹配LAQ的词汇表大小
  modality: "vision,delta"   # 只使用vision和delta模态

training:
  batch_size: 16            # 根据GPU内存调整
  learning_rate: 1e-5       # 学习率
  num_epochs: 5             # 训练轮数
```

## 技术优势

### 1. 效率优势
- **跳过VQ-VAE训练**: 节省大量计算资源和时间
- **利用预训练知识**: LAQ已经学习了你场景的动作表示
- **快速收敛**: 直接监督学习，训练更稳定

### 2. 质量优势
- **场景特化**: LAQ针对你的特定场景训练，提供更准确的latent actions
- **语义一致性**: 保持LAQ学习到的动作语义
- **灵活适配**: 可以根据需要调整词汇表大小和序列长度

### 3. 扩展性优势
- **模块化设计**: 可以轻松替换不同的LAQ模型
- **多模态支持**: 支持vision+delta的多模态学习
- **渐进式训练**: 可以从latent actions逐步过渡到real actions

## 评估指标

训练完成后，系统会计算以下指标：

- **完全匹配准确率**: 预测序列与真实序列完全匹配的比例
- **Token级别准确率**: 单个token预测正确的比例  
- **序列长度准确率**: 预测序列长度正确的比例

## 故障排除

### 常见问题

1. **导入错误**: 确保LAPA路径正确设置
2. **内存不足**: 减小batch_size或使用梯度累积
3. **数据格式错误**: 检查LAQ结果文件格式是否正确
4. **模型加载失败**: 确认模型路径和配置文件正确

### 调试建议

1. **检查数据**: 运行数据预处理脚本查看数据分析报告
2. **小规模测试**: 先用少量数据测试训练流程
3. **监控日志**: 查看训练日志了解训练进度和问题
4. **验证结果**: 使用测试脚本验证模型性能

## 扩展方向

### 1. Action Finetuning
如果你有真实动作数据，可以进一步训练：

```bash
# 在配置文件中启用action finetuning
action_finetuning:
  enabled: true
  real_actions_file: "data/real_actions.jsonl"
```

### 2. 多场景训练
可以合并多个场景的LAQ结果进行训练：

```bash
# 合并多个LAQ结果文件
cat scene1_results.jsonl scene2_results.jsonl > combined_results.jsonl
```

### 3. 在线学习
可以实现在线学习机制，持续使用新的LAQ结果更新模型。

## 总结

这个方案提供了一种高效的方法来训练LAPA模型，通过利用LAQ的预训练知识，避免了从零开始学习latent action quantization的复杂过程。该方法特别适合于：

- 有特定场景数据的应用
- 计算资源有限的环境
- 需要快速原型验证的项目
- 希望利用现有LAQ模型的场景

通过这种方法，你可以快速获得一个针对你特定场景优化的vision-language-action模型。
