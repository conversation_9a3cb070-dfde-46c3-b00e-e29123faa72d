#!/usr/bin/env python3
"""
LAPA Codebook Size兼容性测试脚本

该脚本用于测试：
1. 预训练模型的codebook_size
2. 不同codebook_size参数的兼容性
3. 模型加载时的参数匹配情况
"""

import torch
import sys
import os
from pathlib import Path

# 添加LAPA模块路径
sys.path.append('LAPA/laq')

from laq_model.latent_action_quantization import LatentActionQuantization

def analyze_pretrained_model(checkpoint_path):
    """分析预训练模型的参数"""
    print("=" * 60)
    print("🔍 分析预训练模型参数")
    print("=" * 60)
    
    if not os.path.exists(checkpoint_path):
        print(f"❌ 模型文件不存在: {checkpoint_path}")
        return None
    
    try:
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        print(f"✓ 成功加载检查点: {checkpoint_path}")
        
        # 获取状态字典
        if isinstance(checkpoint, dict):
            if 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
        else:
            state_dict = checkpoint
        
        # 分析codebook参数
        codebook_info = {}
        if 'vq.codebooks' in state_dict:
            codebooks_shape = state_dict['vq.codebooks'].shape
            codebook_info['num_embeddings'] = codebooks_shape[0]
            codebook_info['embedding_dim'] = codebooks_shape[1]
            
            print(f"📊 Codebook参数:")
            print(f"  - Codebook大小 (num_embeddings): {codebook_info['num_embeddings']}")
            print(f"  - 嵌入维度 (embedding_dim): {codebook_info['embedding_dim']}")
        else:
            print("❌ 未找到codebook参数")
            return None
        
        # 分析其他相关参数
        vq_keys = [k for k in state_dict.keys() if k.startswith('vq.')]
        print(f"\n📋 VQ模块参数 ({len(vq_keys)}个):")
        for key in sorted(vq_keys):
            if hasattr(state_dict[key], 'shape'):
                print(f"  - {key}: {state_dict[key].shape}")
        
        return codebook_info
        
    except Exception as e:
        print(f"❌ 分析模型失败: {e}")
        return None

def test_model_compatibility(pretrained_codebook_size, test_codebook_sizes, checkpoint_path):
    """测试不同codebook_size的兼容性"""
    print("\n" + "=" * 60)
    print("🧪 测试Codebook Size兼容性")
    print("=" * 60)
    
    results = {}
    
    for test_size in test_codebook_sizes:
        print(f"\n🔬 测试 codebook_size = {test_size}")
        print("-" * 40)
        
        try:
            # 创建模型
            model = LatentActionQuantization(
                dim=1024,
                quant_dim=32,
                codebook_size=test_size,  # 使用测试的codebook_size
                image_size=256,
                patch_size=32,
                spatial_depth=8,
                temporal_depth=8,
                dim_head=64,
                heads=16,
                code_seq_len=4,
            )
            
            print(f"✓ 模型创建成功 (codebook_size={test_size})")
            
            # 尝试加载预训练权重
            try:
                model.load(checkpoint_path)
                
                # 检查实际加载的codebook大小
                actual_codebook_size = model.vq.codebooks.shape[0]
                
                if actual_codebook_size == pretrained_codebook_size:
                    status = "✅ 完全兼容"
                    details = f"成功加载，codebook大小匹配 ({actual_codebook_size})"
                elif actual_codebook_size == test_size:
                    status = "⚠️ 部分兼容"
                    details = f"加载成功，但使用了配置的大小 ({test_size}) 而非预训练大小 ({pretrained_codebook_size})"
                else:
                    status = "❓ 未知状态"
                    details = f"实际大小 ({actual_codebook_size}) 与预期不符"
                
                results[test_size] = {
                    'status': 'success',
                    'compatibility': status,
                    'details': details,
                    'actual_size': actual_codebook_size
                }
                
                print(f"  {status}: {details}")
                
            except Exception as load_error:
                results[test_size] = {
                    'status': 'load_failed',
                    'compatibility': "❌ 加载失败",
                    'details': str(load_error),
                    'actual_size': None
                }
                print(f"  ❌ 加载失败: {load_error}")
                
        except Exception as create_error:
            results[test_size] = {
                'status': 'create_failed',
                'compatibility': "❌ 创建失败",
                'details': str(create_error),
                'actual_size': None
            }
            print(f"  ❌ 模型创建失败: {create_error}")
    
    return results

def test_inference_capability(checkpoint_path, codebook_size):
    """测试推理能力"""
    print(f"\n" + "=" * 60)
    print(f"🚀 测试推理能力 (codebook_size={codebook_size})")
    print("=" * 60)
    
    try:
        # 创建模型
        model = LatentActionQuantization(
            dim=1024,
            quant_dim=32,
            codebook_size=codebook_size,
            image_size=256,
            patch_size=32,
            spatial_depth=8,
            temporal_depth=8,
            dim_head=64,
            heads=16,
            code_seq_len=4,
        )
        
        # 加载权重
        model.load(checkpoint_path)
        model.eval()
        
        print("✓ 模型加载成功，开始推理测试...")
        
        # 创建测试数据 (batch_size=1, channels=3, frames=2, height=256, width=256)
        test_video = torch.randn(1, 3, 2, 256, 256)
        
        with torch.no_grad():
            # 测试获取codebook indices
            indices = model(test_video, return_only_codebook_ids=True)
            print(f"✓ Codebook indices生成成功: {indices.shape}")
            print(f"  - 索引范围: {indices.min().item()} ~ {indices.max().item()}")
            print(f"  - 唯一索引数量: {len(torch.unique(indices))}")
            
            # 测试完整推理
            reconstructed = model.inference(test_video)
            print(f"✓ 视频重建成功: {reconstructed.shape}")
            
            return True
            
    except Exception as e:
        print(f"❌ 推理测试失败: {e}")
        return False

def print_summary(pretrained_info, compatibility_results):
    """打印总结报告"""
    print("\n" + "=" * 60)
    print("📋 兼容性分析总结")
    print("=" * 60)
    
    print(f"\n🎯 预训练模型信息:")
    print(f"  - Codebook大小: {pretrained_info['num_embeddings']}")
    print(f"  - 嵌入维度: {pretrained_info['embedding_dim']}")
    
    print(f"\n📊 兼容性测试结果:")
    for size, result in compatibility_results.items():
        print(f"  - codebook_size={size}: {result['compatibility']}")
        if result['details']:
            print(f"    详情: {result['details']}")
    
    print(f"\n💡 建议:")
    pretrained_size = pretrained_info['num_embeddings']
    
    print(f"  1. 🎯 最佳选择: 使用与预训练模型相同的codebook_size={pretrained_size}")
    print(f"     - 可以完全利用预训练权重")
    print(f"     - 保证最佳性能和稳定性")
    
    print(f"  2. ⚠️ 修改codebook_size的影响:")
    print(f"     - 会导致codebook权重无法正确加载")
    print(f"     - 需要重新训练或微调codebook部分")
    print(f"     - 可能影响潜在动作的表征质量")
    
    print(f"  3. 🔧 如果必须修改codebook_size:")
    print(f"     - 考虑只加载除codebook外的其他权重")
    print(f"     - 对新的codebook进行适当的初始化")
    print(f"     - 进行必要的微调训练")

def main():
    """主函数"""
    print("🔬 LAPA Codebook Size 兼容性分析工具")
    print("=" * 60)
    
    # 配置
    checkpoint_path = "models/laq_openx.pt"
    test_codebook_sizes = [8, 16, 32, 64, 128, 256, 512, 1024]
    
    # 1. 分析预训练模型
    pretrained_info = analyze_pretrained_model(checkpoint_path)
    if not pretrained_info:
        print("❌ 无法分析预训练模型，退出测试")
        return
    
    pretrained_codebook_size = pretrained_info['num_embeddings']
    
    # 2. 测试兼容性
    compatibility_results = test_model_compatibility(
        pretrained_codebook_size, 
        test_codebook_sizes, 
        checkpoint_path
    )
    
    # 3. 测试推理能力（使用预训练的codebook_size）
    inference_success = test_inference_capability(checkpoint_path, pretrained_codebook_size)
    
    # 4. 打印总结
    print_summary(pretrained_info, compatibility_results)
    
    print(f"\n🎉 分析完成！")
    if inference_success:
        print("✅ 推理测试通过，模型工作正常")
    else:
        print("❌ 推理测试失败，请检查模型配置")

if __name__ == "__main__":
    main()
