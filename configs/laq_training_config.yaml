# LAPA训练配置 - 使用LAQ结果
# 基于LAPA原始配置修改

# 数据配置
data:
  laq_results_file: "/home/<USER>/johnny_ws/lapa_ws/results/laq_fast_inference_results.jsonl"
  processed_data_dir: "/home/<USER>/johnny_ws/lapa_ws/data/laq_processed"
  train_ratio: 0.8
  val_ratio: 0.1
  test_ratio: 0.1

# 模型配置
model:
  name: "lapa_laq_pretrained"
  base_model: "LWM-Chat-1M"  # 使用Large World Model作为backbone
  modality: "vision,delta"   # 只使用vision和delta模态
  delta_vocab_size: 8        # LAQ使用的delta词汇表大小
  freeze_vision_encoder: true
  
# 训练配置
training:
  batch_size: 16
  learning_rate: 1e-5
  num_epochs: 5
  warmup_steps: 100
  save_steps: 500
  eval_steps: 500
  logging_steps: 100
  gradient_accumulation_steps: 1
  max_grad_norm: 1.0
  
# 优化器配置
optimizer:
  type: "AdamW"
  weight_decay: 0.01
  beta1: 0.9
  beta2: 0.999
  eps: 1e-8

# 学习率调度
scheduler:
  type: "cosine"
  warmup_ratio: 0.1
  
# 硬件配置
hardware:
  device: "cuda"
  mixed_precision: true
  dataloader_num_workers: 4
  pin_memory: true

# 输出配置
output:
  checkpoint_dir: "/home/<USER>/johnny_ws/lapa_ws/checkpoints"
  log_dir: "/home/<USER>/johnny_ws/lapa_ws/logs"
  results_dir: "/home/<USER>/johnny_ws/lapa_ws/results"

# 评估配置
evaluation:
  metrics: ["exact_accuracy", "token_accuracy", "length_accuracy"]
  save_predictions: true
  
# 可选的Action Finetuning配置（如果有真实动作数据）
action_finetuning:
  enabled: false
  real_actions_file: "/home/<USER>/johnny_ws/lapa_ws/data/real_actions.jsonl"
  action_vocab_size: 245
  batch_size: 8
  learning_rate: 5e-6
  num_epochs: 3
  replace_action_head: true

# 数据处理配置
preprocessing:
  image_size: 224
  normalize_images: true
  augmentation: false  # 对于latent action学习，通常不需要数据增强
  
# 验证配置
validation:
  check_data_integrity: true
  validate_image_paths: true
  validate_delta_tokens: true
  
# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true
